const mongoose = require('mongoose');
const dotenv = require('dotenv');
const User = require('../models/User');
const Group = require('../models/Group');
const GroupMembership = require('../models/GroupMembership');

// Load environment variables
dotenv.config();

async function createSuperUser() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Check if super user already exists
    const existingSuperUser = await User.findOne({ username: 'rosyman' });
    if (existingSuperUser) {
      console.log('Super user "rosyman" already exists');
      if (!existingSuperUser.isSuperUser) {
        existingSuperUser.isSuperUser = true;
        await existingSuperUser.save();
        console.log('Updated existing user to super user');
      }
      return;
    }

    // Create a special group for super user
    let superGroup = await Group.findOne({ code: 'super-admin' });
    if (!superGroup) {
      superGroup = new Group({
        name: 'Super Administrators',
        code: 'super-admin',
        tier: '100+',
        createdBy: null // Will be set after user creation
      });
    }

    // Create super user
    const superUser = new User({
      username: 'rosyman',
      password: 'ttttt',
      email: '<EMAIL>',
      firstName: 'Super',
      lastName: 'Admin',
      group: superGroup._id,
      groupStatus: 'active',
      isSuperUser: true
    });

    await superUser.save();
    console.log('Super user "rosyman" created successfully');

    // Set the group creator if it wasn't set
    if (!superGroup.createdBy) {
      superGroup.createdBy = superUser._id;
      await superGroup.save();
    }

    // Create group membership
    const membership = new GroupMembership({
      group: superGroup._id,
      user: superUser._id,
      role: 'administrator',
      status: 'active',
      joinedAt: new Date()
    });

    await membership.save();
    console.log('Super user group membership created');

    console.log('Super user setup completed successfully!');
    console.log('Username: rosyman');
    console.log('Password: ttttt');

  } catch (error) {
    console.error('Error creating super user:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
createSuperUser();
