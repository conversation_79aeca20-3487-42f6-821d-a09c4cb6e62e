const mongoose = require('mongoose');
const Requirement = require('../models/Requirement');
const Project = require('../models/Project');
const User = require('../models/User');
const Feature = require('../models/Feature');
const Label = require('../models/Label');

require('dotenv').config();

const resetDatabase = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Delete all collections
    const requirementsResult = await Requirement.deleteMany({});
    const projectsResult = await Project.deleteMany({});
    const usersResult = await User.deleteMany({});
    

    console.log(`Deleted ${requirementsResult.deletedCount} requirements`);
    console.log(`Deleted ${projectsResult.deletedCount} projects`);
    console.log(`Deleted ${usersResult.deletedCount} users`);

    console.log('Database reset completed successfully');
  } catch (error) {
    console.error('Database reset failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

resetDatabase(); 
