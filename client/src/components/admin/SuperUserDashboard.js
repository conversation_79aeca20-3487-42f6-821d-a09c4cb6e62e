import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent,
  Switch,
  FormControlLabel,
  Alert
} from '@mui/material';
import {
  Visibility,
  ToggleOn,
  ToggleOff,
  Group,
  People,
  Business,
  TrendingUp
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import axios from 'axios';

const SuperUserDashboard = ({ open, onClose }) => {
  const { user } = useAuth();
  const [groups, setGroups] = useState([]);
  const [stats, setStats] = useState({});
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [groupDetailsOpen, setGroupDetailsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (open && user?.isSuperUser) {
      loadData();
    }
  }, [open, user]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError('');

      // Load groups and stats
      const [groupsRes, statsRes] = await Promise.all([
        axios.get('/api/admin/groups'),
        axios.get('/api/admin/stats')
      ]);

      setGroups(groupsRes.data);
      setStats(statsRes.data);
    } catch (error) {
      console.error('Error loading admin data:', error);
      setError(error.response?.data?.message || 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleGroupStatus = async (groupId, currentStatus) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      await axios.put(`/api/admin/groups/${groupId}/status`, {
        status: newStatus
      });
      
      // Reload data
      await loadData();
    } catch (error) {
      console.error('Error toggling group status:', error);
      setError(error.response?.data?.message || 'Failed to update group status');
    }
  };

  const handleViewGroup = async (groupId) => {
    try {
      const response = await axios.get(`/api/admin/groups/${groupId}`);
      setSelectedGroup(response.data);
      setGroupDetailsOpen(true);
    } catch (error) {
      console.error('Error loading group details:', error);
      setError(error.response?.data?.message || 'Failed to load group details');
    }
  };

  if (!user?.isSuperUser) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
        <DialogContent>
          <Alert severity="error">
            Access denied. Super user privileges required.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Close</Button>
        </DialogActions>
      </Dialog>
    );
  }

  const getTierColor = (tier) => {
    switch (tier) {
      case '1-3': return '#4caf50';
      case '4-25': return '#2196f3';
      case '26-100': return '#ff9800';
      case '100+': return '#9c27b0';
      default: return '#757575';
    }
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="xl" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Business />
            Super User Dashboard
          </Box>
        </DialogTitle>
        
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {/* Statistics Overview */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Group color="primary" />
                    <Box>
                      <Typography variant="h6">{stats.totalGroups || 0}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Groups
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TrendingUp color="success" />
                    <Box>
                      <Typography variant="h6">{stats.activeGroups || 0}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Active Groups
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <People color="info" />
                    <Box>
                      <Typography variant="h6">{stats.totalUsers || 0}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Users
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <People color="success" />
                    <Box>
                      <Typography variant="h6">{stats.activeUsers || 0}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Active Users
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Groups Table */}
          <Typography variant="h6" sx={{ mb: 2 }}>
            All Groups
          </Typography>
          
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Group Name</TableCell>
                  <TableCell>Code</TableCell>
                  <TableCell>Tier</TableCell>
                  <TableCell>Members</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {groups.map((group) => (
                  <TableRow key={group._id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {group.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Created by: {group.createdBy?.username || 'Unknown'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={group.code} 
                        size="small" 
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={group.tier}
                        size="small"
                        sx={{
                          backgroundColor: getTierColor(group.tier),
                          color: 'white'
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {group.activeMembers} / {group.maxUsers}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {group.availableSeats} available
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={group.status === 'active'}
                            onChange={() => handleToggleGroupStatus(group._id, group.status)}
                            color="primary"
                          />
                        }
                        label={group.status}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(group.createdAt).toLocaleDateString()}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={() => handleViewGroup(group._id)}
                        title="View Details"
                      >
                        <Visibility />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Group Details Dialog */}
      <Dialog 
        open={groupDetailsOpen} 
        onClose={() => setGroupDetailsOpen(false)} 
        maxWidth="md" 
        fullWidth
      >
        <DialogTitle>
          Group Details: {selectedGroup?.name}
        </DialogTitle>
        <DialogContent>
          {selectedGroup && (
            <Box>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Group Code</Typography>
                  <Typography variant="body2">{selectedGroup.code}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Tier</Typography>
                  <Typography variant="body2">{selectedGroup.tier}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Status</Typography>
                  <Typography variant="body2">{selectedGroup.status}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Created</Typography>
                  <Typography variant="body2">
                    {new Date(selectedGroup.createdAt).toLocaleDateString()}
                  </Typography>
                </Grid>
              </Grid>

              <Typography variant="h6" sx={{ mb: 2 }}>
                Members ({selectedGroup.activeMembers})
              </Typography>
              
              <TableContainer component={Paper}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>Username</TableCell>
                      <TableCell>Email</TableCell>
                      <TableCell>Role</TableCell>
                      <TableCell>Status</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {selectedGroup.members?.map((member) => (
                      <TableRow key={member._id}>
                        <TableCell>
                          {member.user.firstName} {member.user.lastName}
                        </TableCell>
                        <TableCell>{member.user.username}</TableCell>
                        <TableCell>{member.user.email}</TableCell>
                        <TableCell>
                          <Chip
                            label={member.role}
                            size="small"
                            color={member.role === 'administrator' ? 'primary' : 'default'}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={member.status}
                            size="small"
                            color={member.status === 'active' ? 'success' : 'default'}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setGroupDetailsOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default SuperUserDashboard;
