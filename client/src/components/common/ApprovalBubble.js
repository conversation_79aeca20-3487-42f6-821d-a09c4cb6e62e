import React from 'react';
import { <PERSON>, Icon<PERSON>utton, Tooltip, Chip } from '@mui/material';
import { Check as CheckIcon } from '@mui/icons-material';
import EnhancedAvatar from './EnhancedAvatar';

const ApprovalBubble = ({ 
  user, 
  isApprover, 
  isApproved, 
  canToggleApproval, 
  onToggleApproval,
  roles = []
}) => {
  if (!user || !user.username) {
    console.error('Invalid user data:', user);
    return null;
  }

  const rolesText = roles && roles.length > 0
    ? roles.map(role =>
        role.split('_').map(word =>
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ')
      ).join(', ')
    : '';

  const displayText = rolesText
    ? `${user.username} (${rolesText})`
    : user.username;

  const handleApprovalClick = (e) => {
    e.stopPropagation();
    if (canToggleApproval && onToggleApproval) {
      onToggleApproval();
    }
  };

  return (
    <Chip
      label={displayText}
      avatar={<EnhancedAvatar user={user} size={24} sx={{ color: '#FFFFFF !important' }} />}
      sx={{
        bgcolor: isApproved ? '#4caf50' : (user.color || '#1976d2') + '20',
        m: 0.5,
        '& .MuiChip-label': {
          color: isApproved ? 'white' : 'inherit'
        },
        '& .MuiChip-avatar': {
          color: '#FFFFFF !important'
        }
      }}
      deleteIcon={
        isApprover ? (
          <Tooltip title={isApproved ? "Remove approval" : "Approve"}>
            <CheckIcon
              sx={{
                color: isApproved ? '#ffffff' : '#666666',
                cursor: canToggleApproval ? 'pointer' : 'default',
                opacity: canToggleApproval ? 1 : 0.5,
                fontWeight: isApproved ? 'bold' : 'normal',
                // Add subtle shadow for approved checkmarks to make them more prominent
                textShadow: isApproved ? '0 1px 2px rgba(0,0,0,0.3)' : 'none',
                // Slightly larger size for approved checkmarks
                fontSize: isApproved ? '1.3rem' : '1.2rem',
                transition: 'all 0.2s ease-in-out'
              }}
            />
          </Tooltip>
        ) : null
      }
      onDelete={isApprover && canToggleApproval ? handleApprovalClick : undefined}
    />
  );
};

export default ApprovalBubble;
